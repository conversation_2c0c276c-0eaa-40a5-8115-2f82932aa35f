package vn.vinclub.shield.constant;

import org.redisson.client.codec.Codec;
import org.redisson.client.codec.IntegerCodec;
import org.redisson.codec.TypedJsonJacksonCodec;

public interface AppConst {
    String FIREBASE_APP_CHECK_TOKEN_SERVICE = "firebaseAppCheckTokenService";
    String FIREBASE_APP_CHECK_TOKEN_HEADER_KEY = "X-Firebase-AppCheck";

    String DEFAULT_LANGUAGE = "vi";
    String DEFAULT_COUNTRY = "VN";



    interface CacheCodec {
        Codec INTEGER_CODEC = new IntegerCodec();
        Codec STRING_LONG_CODEC = new TypedJsonJacksonCodec(String.class, Long.class);
    }
}
