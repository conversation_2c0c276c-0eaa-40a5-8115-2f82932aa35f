package vn.vinclub.shield.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * Utility class for RSA key management
 */
@Slf4j
public class RSAKeyUtils {

    private static final String RSA_ALGORITHM = "RSA";
    private static final int KEY_SIZE = 2048;
    private static final String PRIVATE_KEY_HEADER = "-----BEGIN PRIVATE KEY-----";
    private static final String PRIVATE_KEY_FOOTER = "-----END PRIVATE KEY-----";
    private static final String PUBLIC_KEY_HEADER = "-----BEGIN PUBLIC KEY-----";
    private static final String PUBLIC_KEY_FOOTER = "-----END PUBLIC KEY-----";

    /**
     * Generate a new RSA key pair with 2048-bit key size
     *
     * @return RSA key pair
     * @throws RuntimeException if key generation fails
     */
    public static KeyPair generateKeyPair() {
        try {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(RSA_ALGORITHM);
            keyPairGenerator.initialize(KEY_SIZE);
            KeyPair keyPair = keyPairGenerator.generateKeyPair();
            log.info("Generated new RSA key pair with {} bit key size", KEY_SIZE);
            return keyPair;
        } catch (NoSuchAlgorithmException e) {
            log.error("Failed to generate RSA key pair", e);
            throw new RuntimeException("Failed to generate RSA key pair", e);
        }
    }

    /**
     * Load private key from PEM string
     *
     * @param privateKeyPem PEM formatted private key string
     * @return PrivateKey instance
     * @throws RuntimeException if key loading fails
     */
    public static PrivateKey loadPrivateKey(String privateKeyPem) {
        if (!StringUtils.hasText(privateKeyPem)) {
            throw new IllegalArgumentException("Private key PEM string cannot be null or empty");
        }

        try {
            // Remove PEM headers/footers and whitespace
            String privateKeyContent = privateKeyPem
                    .replace(PRIVATE_KEY_HEADER, "")
                    .replace(PRIVATE_KEY_FOOTER, "")
                    .replaceAll("\\s", "");

            // Decode base64
            byte[] keyBytes = Base64.getDecoder().decode(privateKeyContent);

            // Create private key
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
            PrivateKey privateKey = keyFactory.generatePrivate(keySpec);
            
            log.info("Successfully loaded RSA private key from PEM string");
            return privateKey;
        } catch (Exception e) {
            log.error("Failed to load private key from PEM string", e);
            throw new RuntimeException("Failed to load private key from PEM string", e);
        }
    }

    /**
     * Load public key from PEM string
     *
     * @param publicKeyPem PEM formatted public key string
     * @return PublicKey instance
     * @throws RuntimeException if key loading fails
     */
    public static PublicKey loadPublicKey(String publicKeyPem) {
        if (!StringUtils.hasText(publicKeyPem)) {
            throw new IllegalArgumentException("Public key PEM string cannot be null or empty");
        }

        try {
            // Remove PEM headers/footers and whitespace
            String publicKeyContent = publicKeyPem
                    .replace(PUBLIC_KEY_HEADER, "")
                    .replace(PUBLIC_KEY_FOOTER, "")
                    .replaceAll("\\s", "");

            // Decode base64
            byte[] keyBytes = Base64.getDecoder().decode(publicKeyContent);

            // Create public key
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
            PublicKey publicKey = keyFactory.generatePublic(keySpec);
            
            log.info("Successfully loaded RSA public key from PEM string");
            return publicKey;
        } catch (Exception e) {
            log.error("Failed to load public key from PEM string", e);
            throw new RuntimeException("Failed to load public key from PEM string", e);
        }
    }

    /**
     * Convert private key to PEM format string
     *
     * @param privateKey PrivateKey instance
     * @return PEM formatted private key string
     */
    public static String privateKeyToPem(PrivateKey privateKey) {
        byte[] encoded = privateKey.getEncoded();
        String base64Encoded = Base64.getEncoder().encodeToString(encoded);
        
        StringBuilder pem = new StringBuilder();
        pem.append(PRIVATE_KEY_HEADER).append("\n");
        
        // Split into 64-character lines
        for (int i = 0; i < base64Encoded.length(); i += 64) {
            int endIndex = Math.min(i + 64, base64Encoded.length());
            pem.append(base64Encoded, i, endIndex).append("\n");
        }
        
        pem.append(PRIVATE_KEY_FOOTER);
        return pem.toString();
    }

    /**
     * Convert public key to PEM format string
     *
     * @param publicKey PublicKey instance
     * @return PEM formatted public key string
     */
    public static String publicKeyToPem(PublicKey publicKey) {
        byte[] encoded = publicKey.getEncoded();
        String base64Encoded = Base64.getEncoder().encodeToString(encoded);
        
        StringBuilder pem = new StringBuilder();
        pem.append(PUBLIC_KEY_HEADER).append("\n");
        
        // Split into 64-character lines
        for (int i = 0; i < base64Encoded.length(); i += 64) {
            int endIndex = Math.min(i + 64, base64Encoded.length());
            pem.append(base64Encoded, i, endIndex).append("\n");
        }
        
        pem.append(PUBLIC_KEY_FOOTER);
        return pem.toString();
    }
}
