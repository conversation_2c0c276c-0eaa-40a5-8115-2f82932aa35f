package vn.vinclub.shield.util;

import org.springframework.util.StringUtils;
import vn.vinclub.shield.dto.CustomerIdentity;
import vn.vinclub.shield.dto.DeviceFingerprint;
import vn.vinclub.shield.dto.EvaluationData;
import vn.vinclub.shield.dto.TrackingContext;

import java.util.ArrayList;
import java.util.List;

/**
 * Utility class for converting between different data structures
 */
public class TrackingContextUtils {

    /**
     * Convert EvaluationData to TrackingContext
     */
    public static TrackingContext fromEvaluationData(EvaluationData evaluationData) {
        if (evaluationData == null) {
            return null;
        }

        return TrackingContext.builder()
                .action(evaluationData.getAction())
                .actionKey(evaluationData.getKey())
                .ipAddress(evaluationData.getIpAddress())
                .customerIdentifiers(createCustomerIdentifiers(evaluationData.getCustomerIdentity()))
                .deviceIdentifiers(createDeviceIdentifiers(evaluationData.getDeviceFingerprint()))
                .build();
    }


    /**
     * Creates unique identifiers for customer data based on all available fields
     * Returns all non-empty fields as separate identifiers
     *
     * @param customerIdentity the customer data
     * @return list of unique identifier strings, empty list if no valid data
     */
    private static List<String> createCustomerIdentifiers(CustomerIdentity customerIdentity) {
        List<String> identifiers = new ArrayList<>();

        if (StringUtils.hasText(customerIdentity.getCustomerId())) {
            identifiers.add("cid:" + customerIdentity.getCustomerId());
        }
        if (StringUtils.hasText(customerIdentity.getEmail())) {
            identifiers.add("email:" + customerIdentity.getEmail());
        }
        if (StringUtils.hasText(customerIdentity.getPhone())) {
            identifiers.add("phone:" + customerIdentity.getPhone());
        }

        return identifiers;
    }



    /**
     * Creates unique identifiers for device data based on all available fields
     * Returns all non-empty fields as separate identifiers
     *
     * @param deviceFingerprint the device data
     * @return list of unique identifier strings, empty list if no valid data
     */
    private static List<String> createDeviceIdentifiers(DeviceFingerprint deviceFingerprint) {
        List<String> identifiers = new ArrayList<>();

        if (StringUtils.hasText(deviceFingerprint.getDeviceId())) {
            identifiers.add("did:" + deviceFingerprint.getDeviceId());
        }
        return identifiers;
    }
}
