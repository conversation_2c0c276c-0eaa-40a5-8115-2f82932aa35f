package vn.vinclub.shield.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.shield.enums.Action;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationData {
    private Action action;
    private String key;
    private CustomerIdentity customerIdentity;
    private DeviceFingerprint deviceFingerprint;
    private String ipAddress;
    private String userAgent;
    private Map<String, Object> metadata;
    private String requestId;
}
