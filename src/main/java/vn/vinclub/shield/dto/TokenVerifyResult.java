package vn.vinclub.shield.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.shield.enums.AppErrorCode;

/**
 * Response model for token verification.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TokenVerifyResult {

    private boolean valid;

    private AppErrorCode failureReason;

    public static TokenVerifyResult valid() {
        return TokenVerifyResult.builder()
                .valid(true)
                .build();
    }

    public static TokenVerifyResult invalid(AppErrorCode errorCode) {
        return TokenVerifyResult.builder()
                .valid(false)
                .failureReason(errorCode)
                .build();
    }
}
