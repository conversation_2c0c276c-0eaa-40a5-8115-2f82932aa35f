package vn.vinclub.shield.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.shield.dto.ShieldEvaluationResult;
import vn.vinclub.shield.dto.ShieldVerifyResult;
import vn.vinclub.shield.enums.AppErrorCode;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiProtectResponse {
    private int state; // 0: evaluation state, 1: verify state

    private ShieldEvaluationResult evaluationResult;
    private ShieldVerifyResult verifyResult;

    public static ApiProtectResponse evaluate(ShieldEvaluationResult evaluationResult) {
        return ApiProtectResponse.builder()
                .state(0)
                .evaluationResult(evaluationResult)
                .build();
    }

    public static ApiProtectResponse verifyPass() {
        return ApiProtectResponse.builder()
                .state(1)
                .verifyResult(ShieldVerifyResult.pass())
                .build();
    }
    public static ApiProtectResponse verifyNotPass(AppErrorCode errorCode) {
        return ApiProtectResponse.builder()
                .state(1)
                .verifyResult(ShieldVerifyResult.notPass(errorCode))
                .build();
    }
}
