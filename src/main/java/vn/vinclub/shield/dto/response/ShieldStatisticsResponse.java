package vn.vinclub.shield.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.shield.enums.StatisticTimePeriod;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Response DTO for shield protection statistics
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShieldStatisticsResponse {
    
    /**
     * Time period for these statistics
     */
    private StatisticTimePeriod timePeriod;
    
    /**
     * Timestamp when statistics were retrieved
     */
    private LocalDateTime retrievedAt;
    
    /**
     * Core metrics
     */
    private CoreMetrics coreMetrics;
    
    /**
     * Risk category breakdown
     */
    private RiskCategoryMetrics riskCategoryMetrics;
    
    /**
     * Raw metrics map for additional data
     */
    private Map<String, Long> additionalMetrics;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CoreMetrics {
        private Long evaluationPass;
        private Long protectionRequired;
        private Long shieldVerificationSuccess;
        private Long shieldVerificationFailed;

        public Long getTotalEvaluationRequests() {
            return (evaluationPass != null ? evaluationPass : 0) +
                   (protectionRequired != null ? protectionRequired : 0);
        }

        public Double getProtectionRate() {
            long totalRequests = getTotalEvaluationRequests();
            if (totalRequests == 0) {
                return 0.0;
            }
            return (protectionRequired != null ? protectionRequired : 0) * 100.0 / totalRequests;
        }

        public Double getEvaluationPassRate() {
            return 100.0 - getProtectionRate();
        }


        public Long getTotalVerificationRequests() {
            return (shieldVerificationSuccess != null ? shieldVerificationSuccess : 0) +
                   (shieldVerificationFailed != null ? shieldVerificationFailed : 0);
        }

        public Double getVerificationSuccessRate() {
            long totalVerifications = getTotalVerificationRequests();
            if (totalVerifications == 0) {
                return 0.0;
            }
            return (shieldVerificationSuccess != null ? shieldVerificationSuccess : 0) * 100.0 / totalVerifications;
        }

        public Double getVerificationFailureRate() {
            return 100.0 - getVerificationSuccessRate();
        }
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RiskCategoryMetrics {
        private Long fraudDetection;
        private Long velocityLimits;
        private Long behavioralAnomalies;
        private Long networkAnomalies;
        private Long deviceAnomalies;
        private Long temporalAnomalies;
        private Long userIdentityAnomalies;
        private Long crossCorrelation;
        private Long analyzeData;
    }
}
