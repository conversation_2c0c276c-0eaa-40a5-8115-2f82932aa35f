package vn.vinclub.shield.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.shield.dto.CustomerIdentity;
import vn.vinclub.shield.dto.DeviceFingerprint;
import vn.vinclub.shield.dto.EvaluationData;
import vn.vinclub.shield.dto.ProtectTokenData;
import vn.vinclub.shield.enums.Action;

import java.util.List;
import java.util.Map;

/**
 * Request model for verifying tokens.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApiProtectRequest {

    /**
     * The action being performed, which determines token reuse rules. (The action is represented for an API endpoint)
     * @see Action
     */
    private Action action;

    /**
     * The key associated with the action. Use to protect with the same API but different purpose.
     */
    private String key;

    /**
     * The customer data
     */
    private CustomerIdentity customerIdentity;

    /**
     * The device data
     */
    private DeviceFingerprint deviceFingerprint;

    /**
     * The IP request Id
     */
    private String requestId;

    /**
     * The IP address of the request.
     */
    private String ipAddress;

    /**
     * The user agent of the request.
     */
    private String userAgent;

    /**
     * The list of tokens to verify.
     */
    private List<ProtectTokenData> protectTokens;

    /**
     * The Shield token to verify checked data from shield service.
     */
    private String shieldToken;

    /**
     * Additional metadata associated with the request.
     */
    private Map<String, Object> metadata;


    /**
     * Convert to EvaluationData
     * @return EvaluationData
     */
    public EvaluationData toEvaluateData() {
        return EvaluationData.builder()
                .action(action)
                .key(key)
                .customerIdentity(customerIdentity)
                .deviceFingerprint(deviceFingerprint)
                .requestId(requestId)
                .ipAddress(ipAddress)
                .userAgent(userAgent)
                .metadata(metadata)
                .build();
    }

}