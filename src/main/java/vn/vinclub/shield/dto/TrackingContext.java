package vn.vinclub.shield.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import vn.vinclub.shield.enums.Action;

import java.util.List;

/**
 * Context object containing all tracking data for a single API call
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrackingContext {
    private Action action;
    private String actionKey;
    private String ipAddress;
    private List<String> customerIdentifiers;
    private List<String> deviceIdentifiers;
    
    /**
     * Check if customer data is available for tracking
     */
    public boolean hasCustomerData() {
        return !CollectionUtils.isEmpty(customerIdentifiers);
    }
    
    /**
     * Check if device data is available for tracking
     */
    public boolean hasDeviceData() {
        return !CollectionUtils.isEmpty(deviceIdentifiers);
    }
    
    /**
     * Check if IP address is available for tracking
     */
    public boolean hasIpAddress() {
        return StringUtils.hasText(ipAddress);
    }
    
    /**
     * Check if action key is available for tracking
     */
    public boolean hasActionKey() {
        return StringUtils.hasText(actionKey);
    }
}
