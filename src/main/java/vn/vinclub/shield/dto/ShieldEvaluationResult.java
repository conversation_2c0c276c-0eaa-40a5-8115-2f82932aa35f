package vn.vinclub.shield.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.shield.enums.AppErrorCode;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShieldEvaluationResult {
    private boolean pass;
    private int code;
    private String message;
    private String shieldToken;
    private Long holdTimeInSecond;


    public static ShieldEvaluationResult notPass(String shieldToken, Long holdTimeInSecond) {
        return ShieldEvaluationResult.builder()
                .pass(false)
                .shieldToken(shieldToken)
                .holdTimeInSecond(holdTimeInSecond)
                .code(AppErrorCode.SHIELD_PROTECT_REQUIRED.getCode())
                .message(AppErrorCode.SHIELD_PROTECT_REQUIRED.getCurrentLocalMessage())
                .build();
    }

    public static ShieldEvaluationResult pass() {
        return ShieldEvaluationResult.builder()
                .code(AppErrorCode.SUCCESS.getCode())
                .message(AppErrorCode.SUCCESS.getCurrentLocalMessage())
                .pass(true)
                .build();
    }
}
