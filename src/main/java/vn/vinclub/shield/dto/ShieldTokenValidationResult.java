package vn.vinclub.shield.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * Result of shield token validation containing validity status and time range
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShieldTokenValidationResult {
    
    /**
     * Whether the token is valid
     */
    private boolean valid;
    
    /**
     * Token issued at time (iat) - only present if valid
     */
    private Instant issuedAt;
    
    /**
     * Token expires at time (exp) - only present if valid
     */
    private Instant expiresAt;
    
    /**
     * Reason for validation failure - only present if invalid
     */
    private String failureReason;
    
    /**
     * Create a valid result with time range
     */
    public static ShieldTokenValidationResult valid(Instant issuedAt, Instant expiresAt) {
        return ShieldTokenValidationResult.builder()
                .valid(true)
                .issuedAt(issuedAt)
                .expiresAt(expiresAt)
                .build();
    }
    
    /**
     * Create an invalid result with failure reason
     */
    public static ShieldTokenValidationResult invalid(String failureReason) {
        return ShieldTokenValidationResult.builder()
                .valid(false)
                .failureReason(failureReason)
                .build();
    }
    
    /**
     * Create an empty result (invalid with no specific reason)
     */
    public static ShieldTokenValidationResult empty() {
        return ShieldTokenValidationResult.builder()
                .valid(false)
                .build();
    }
    
    /**
     * Check if the result has a valid time range
     */
    public boolean hasValidRange() {
        return valid && issuedAt != null && expiresAt != null;
    }
}
