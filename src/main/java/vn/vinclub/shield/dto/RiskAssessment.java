package vn.vinclub.shield.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Risk assessment result containing detailed analysis of tracking data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskAssessment {
    
    /**
     * Number of risk factors detected
     */
    private int numberOfRisks;
    
    /**
     * Whether protection should be triggered
     */
    private boolean requiresProtection;
    
    /**
     * List of risk factors that contributed to the score
     */
    @Builder.Default
    private List<RiskFactor> riskFactors = new ArrayList<>();
    
    /**
     * Detailed tracking results for each data type
     */
    private TrackingResults trackingResults;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskFactor {
        private String type;
        private String description;
        
        public static RiskFactor of(String type, String description) {
            return RiskFactor.builder()
                    .type(type)
                    .description(description)
                    .build();
        }
    }
}
