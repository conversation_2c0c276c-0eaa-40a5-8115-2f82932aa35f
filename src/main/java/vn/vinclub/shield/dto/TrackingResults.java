package vn.vinclub.shield.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Comprehensive fraud detection tracking results containing behavioral analytics,
 * device fingerprinting, and risk assessment indicators across multiple time periods.
 * <p>
 * This class encapsulates the results of analyzing user behavior patterns, device characteristics,
 * network attributes, and temporal patterns to identify potential fraudulent activities.
 *
 * <AUTHOR> Shield Service
 * @version 2.0
 * @since 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrackingResults {

    // ========================================
    // NETWORK BEHAVIOR ANALYTICS
    // ========================================

    /**
     * Indicates if the IP address has never been seen across the entire system.
     */
    private boolean ipAddressFirstTimeSeen;

    /**
     * Indicates if the IP address is new within the current month.
     */
    private boolean ipAddressFirstThisMonth;

    /**
     * Indicates if the IP address is new within the current day.
     */
    private boolean ipAddressFirstToday;

    /**
     * Indicates if the IP address is new within the current action.
     */
    private boolean ipAddressFirstActionToday;


    // ========================================
    // USER IDENTITY ANALYTICS
    // ========================================

    /**
     * Indicates if the customer identifiers have never been seen across the entire system.
     */
    private boolean userIdentityFirstTimeSeen;

    /**
     * Indicates if the customer identifiers are new within the current month.
     */
    private boolean userIdentityFirstThisMonth;

    /**
     * Indicates if the customer identifiers are new within the current day.
     */
    private boolean userIdentityFirstToday;

    /**
     * Indicates if the customer identifiers are new within the current action.
     */
    private boolean userIdentityFirstActionToday;

    // ========================================
    // DEVICE FINGERPRINTING ANALYTICS
    // ========================================

    /**
     * Indicates if the device fingerprint has never been seen across the entire system.
     */
    private boolean deviceFingerprintFirstTimeSeen;

    /**
     * Indicates if the device fingerprint is new within the current month.
     */
    private boolean deviceFingerprintFirstThisMonth;

    /**
     * Indicates if the device fingerprint is new within the current day.
     */
    private boolean deviceFingerprintFirstToday;

    /**
     * Indicates if the device fingerprint is new within the current action.
     */
    private boolean deviceFingerprintFirstActionToday;

    // ========================================
    // BEHAVIORAL ANOMALY DETECTION
    // ========================================

    /**
     * Indicates if a new user identity is accessing the system from a previously known IP address.
     */
    private boolean behaviorNewUserFromKnownNetwork;

    /**
     * Indicates if a known user identity is accessing the system from a completely new IP address.
     */
    private boolean behaviorKnownUserFromNewNetwork;

    /**
     * Indicates if a new device fingerprint is accessing the system from a previously known IP address.
     */
    private boolean behaviorNewDeviceFromKnownNetwork;

    /**
     * Indicates if a known device fingerprint is accessing the system from a completely new IP address.
     */
    private boolean behaviorKnownDeviceFromNewNetwork;

    /**
     * Indicates if a new user identity is accessing the system from a previously known device.
     */
    private boolean behaviorNewUserFromKnownDevice;

    /**
     * Indicates if a known user identity is accessing the system from a completely new device.
     */
    private boolean behaviorKnownUserFromNewDevice;

    // ========================================
    // VELOCITY AND FREQUENCY ANALYTICS
    // ========================================

    /**
     * Indicates if multiple new indicators were detected within a short time frame.
     */
    private boolean velocityMultipleNewIndicatorsDetected;

    /**
     * Indicates if the request frequency from this network address exceeds normal patterns.
     */
    private boolean velocityHighFrequencyFromNetwork;

    /**
     * Indicates if the user identity shows unusual activity patterns compared to historical behavior.
     */
    private boolean velocityUnusualUserActivityPattern;

    // ========================================
    // TEMPORAL PATTERN ANALYTICS
    // ========================================

    /**
     * Indicates if the request timing falls outside normal business hours or expected usage patterns.
     */
    private boolean temporalOffHoursActivityDetected;

    /**
     * Indicates if the request pattern shows signs of automated or scripted behavior.
     */
    private boolean temporalAutomatedBehaviorPattern;

    /**
     * Indicates if multiple rapid-fire requests were detected from the same source.
     */
    private boolean temporalRapidFireRequestPattern;


    // ========================================
    // ENHANCED FRAUD DETECTION ANALYTICS
    // ========================================

    /**
     * Checks if any network-related indicators suggest potential fraud.
     * Combines all network behavior analytics for comprehensive assessment.
     *
     * @return true if any network-based fraud indicators are present
     */
    public boolean hasNetworkFraudIndicators() {
        return ipAddressFirstTimeSeen
                || ipAddressFirstThisMonth
                || ipAddressFirstToday
                || ipAddressFirstActionToday;
    }

    /**
     * Checks if any user identity-related indicators suggest potential fraud.
     * Combines all user behavior analytics for comprehensive assessment.
     *
     * @return true if any user identity-based fraud indicators are present
     */
    public boolean hasUserIdentityFraudIndicators() {
        return userIdentityFirstTimeSeen
                || userIdentityFirstThisMonth
                || userIdentityFirstToday
                || userIdentityFirstActionToday;
    }

    /**
     * Checks if any device fingerprinting indicators suggest potential fraud.
     * Combines all device behavior analytics for comprehensive assessment.
     *
     * @return true if any device-based fraud indicators are present
     */
    public boolean hasDeviceFraudIndicators() {
        return deviceFingerprintFirstTimeSeen
                || deviceFingerprintFirstThisMonth
                || deviceFingerprintFirstToday
                || deviceFingerprintFirstActionToday;
    }


    /**
     * Checks if any behavioral anomalies are detected.
     * Combines cross-correlation analysis for suspicious behavior patterns.
     *
     * @return true if any behavioral anomalies are detected
     */
    public boolean hasBehavioralAnomalies() {
        return behaviorNewUserFromKnownNetwork
                || behaviorKnownUserFromNewNetwork
                || behaviorNewDeviceFromKnownNetwork
                || behaviorKnownDeviceFromNewNetwork
                || behaviorNewUserFromKnownDevice
                || behaviorKnownUserFromNewDevice;
    }

    /**
     * Checks if any velocity-based fraud indicators are present.
     * Combines all velocity and frequency analytics for comprehensive assessment.
     *
     * @return true if any velocity-based fraud indicators are present
     */
    public boolean hasVelocityFraudIndicators() {
        return velocityMultipleNewIndicatorsDetected
                || velocityHighFrequencyFromNetwork
                || velocityUnusualUserActivityPattern;
    }

    /**
     * Checks if any temporal pattern anomalies are detected.
     * Combines all temporal analytics for suspicious timing patterns.
     *
     * @return true if any temporal anomalies are detected
     */
    public boolean hasTemporalAnomalies() {
        return temporalOffHoursActivityDetected
                || temporalAutomatedBehaviorPattern
                || temporalRapidFireRequestPattern;
    }

    /**
     * Gets a detailed fraud risk assessment summary.
     * Provides human-readable description of detected risk factors.
     *
     * @return detailed risk assessment summary
     */
    public String getFraudRiskSummary() {
        StringBuilder summary = new StringBuilder();

        // Add detected anomaly details
        if (hasNetworkFraudIndicators()) summary.append(" | Network anomalies detected");
        if (hasUserIdentityFraudIndicators()) summary.append(" | User identity anomalies detected");
        if (hasDeviceFraudIndicators()) summary.append(" | Device anomalies detected");
        if (hasVelocityFraudIndicators()) summary.append(" | Velocity anomalies detected");
        if (hasBehavioralAnomalies()) summary.append(" | Behavioral anomalies detected");
        if (hasTemporalAnomalies()) summary.append(" | Temporal anomalies detected");


        StringBuilder printer = new StringBuilder();
        if (summary.isEmpty()) {
            printer.append("No fraud risk detected");
        } else {
            printer.append("Fraud Risk Assessment: ");
            printer.append(summary.substring(3));
        }
        return printer.toString();
    }

    public int getNumberOfRisks() {
        int count = 0;
        if (isIpAddressFirstTimeSeen()) count++;
        if (isIpAddressFirstThisMonth()) count++;
        if (isIpAddressFirstToday()) count++;
        if (isIpAddressFirstActionToday()) count++;
        if (isUserIdentityFirstTimeSeen()) count++;
        if (isUserIdentityFirstThisMonth()) count++;
        if (isUserIdentityFirstToday()) count++;
        if (isUserIdentityFirstActionToday()) count++;
        if (isDeviceFingerprintFirstTimeSeen()) count++;
        if (isDeviceFingerprintFirstThisMonth()) count++;
        if (isDeviceFingerprintFirstToday()) count++;
        if (isDeviceFingerprintFirstActionToday()) count++;
        if (isBehaviorNewUserFromKnownNetwork()) count++;
        if (isBehaviorKnownUserFromNewNetwork()) count++;
        if (isBehaviorNewDeviceFromKnownNetwork()) count++;
        if (isBehaviorKnownDeviceFromNewNetwork()) count++;
        if (isBehaviorNewUserFromKnownDevice()) count++;
        if (isBehaviorKnownUserFromNewDevice()) count++;
        if (isVelocityMultipleNewIndicatorsDetected()) count++;
        if (isVelocityHighFrequencyFromNetwork()) count++;
        if (isVelocityUnusualUserActivityPattern()) count++;
        if (isTemporalOffHoursActivityDetected()) count++;
        if (isTemporalAutomatedBehaviorPattern()) count++;
        if (isTemporalRapidFireRequestPattern()) count++;
        return count;
    }
}
