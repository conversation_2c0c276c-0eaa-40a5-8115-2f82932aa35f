package vn.vinclub.shield.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.shield.enums.AppErrorCode;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShieldVerifyResult {
    private boolean pass;
    private int code;
    private String message;

    public static ShieldVerifyResult pass() {
        return ShieldVerifyResult.builder()
                .pass(true)
                .code(AppErrorCode.SUCCESS.getCode())
                .message(AppErrorCode.SUCCESS.getCurrentLocalMessage())
                .build();
    }

    public static ShieldVerifyResult notPass(AppErrorCode errorCode) {
        return ShieldVerifyResult.builder()
                .pass(false)
                .code(AppErrorCode.SHIELD_TOKEN_INVALID.getCode())
                .message(errorCode.getCurrentLocalMessage())
                .build();
    }
}
