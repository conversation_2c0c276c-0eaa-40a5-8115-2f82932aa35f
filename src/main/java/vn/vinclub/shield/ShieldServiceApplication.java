package vn.vinclub.shield;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration;
import vn.vinclub.common.annotation.EnableProfiler;

@SpringBootApplication(scanBasePackages = {"vn.vinclub.shield"},
        exclude = {
                DataSourceAutoConfiguration.class,
                HibernateJpaAutoConfiguration.class,
                UserDetailsServiceAutoConfiguration.class
        })
@EnableProfiler
@Slf4j
public class ShieldServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(ShieldServiceApplication.class, args);
    }
}
