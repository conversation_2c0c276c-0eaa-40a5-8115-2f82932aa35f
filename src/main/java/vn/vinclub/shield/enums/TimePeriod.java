package vn.vinclub.shield.enums;

import lombok.Getter;

import java.time.LocalDateTime;

/**
 * Enumeration of time periods for bloom filter tracking
 */
@Getter
public enum TimePeriod {
    ALL_TIME("all_time"),
    MONTHLY("month"),
    DAILY("date");

    private final String keyPrefix;

    TimePeriod(String keyPrefix) {
        this.keyPrefix = keyPrefix;
    }

    /**
     * Get the time-based suffix for the key
     */
    public String getTimeSuffix() {
        return switch (this) {
            case ALL_TIME -> "";
            case MONTHLY -> LocalDateTime.now().toLocalDate().toString().replace("-", "").substring(0, 6);
            case DAILY -> LocalDateTime.now().toLocalDate().toString().replace("-", "");
            default -> throw new IllegalArgumentException("Unknown time period: " + this);
        };
    }

    /**
     * Get the next time period suffix
     */
    public String getNextTimeSuffix() {
        return switch (this) {
            case ALL_TIME -> "";
            case MONTHLY -> LocalDateTime.now().plusMonths(1).toLocalDate().toString().replace("-", "").substring(0, 6);
            case DAILY -> LocalDateTime.now().plusDays(1).toLocalDate().toString().replace("-", "");
            default -> throw new IllegalArgumentException("Unknown time period: " + this);
        };
    }

    /**
     * Get the previous time period suffix
     */
    public String getPreviousTimeSuffix() {
        return switch (this) {
            case ALL_TIME -> "";
            case MONTHLY ->
                    LocalDateTime.now().minusMonths(1).toLocalDate().toString().replace("-", "").substring(0, 6);
            case DAILY -> LocalDateTime.now().minusDays(1).toLocalDate().toString().replace("-", "");
            default -> throw new IllegalArgumentException("Unknown time period: " + this);
        };
    }
}
