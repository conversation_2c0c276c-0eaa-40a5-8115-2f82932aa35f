package vn.vinclub.shield.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Enum representing different types of shield protection metrics
 * that can be tracked and analyzed for monitoring and analytics.
 */
@Getter
@RequiredArgsConstructor
public enum StatisticMetricType {
    
    // Core request metrics
    EVALUATION_PASS("evaluation_pass", "Number of requests that passed evaluation"),
    PROTECTION_REQUIRED("protection_required", "Number of requests that require shield protection"),
    SHIELD_VERIFICATION_SUCCESS("shield_verification_success", "Number of successful shield verifications"),
    SHIELD_VERIFICATION_FAILED("shield_verification_failed", "Number of failed shield verifications"),
    
    // Risk category specific metrics
    PROTECTION_REQUIRED_FRAUD_DETECTION("protection_required_fraud_detection", "Protection required due to fraud detection"),
    PROTECTION_REQUIRED_VELOCITY_LIMITS("protection_required_velocity_limits", "Protection required due to velocity limits"),
    PROTECTION_REQUIRED_BEHAVIORAL_ANOMALIES("protection_required_behavioral_anomalies", "Protection required due to behavioral anomalies"),
    PROTECTION_REQUIRED_NETWORK_ANOMALIES("protection_required_network_anomalies", "Protection required due to network anomalies"),
    PROTECTION_REQUIRED_DEVICE_ANOMALIES("protection_required_device_anomalies", "Protection required due to device anomalies"),
    PROTECTION_REQUIRED_TEMPORAL_ANOMALIES("protection_required_temporal_anomalies", "Protection required due to temporal anomalies"),
    PROTECTION_REQUIRED_USER_IDENTITY_ANOMALIES("protection_required_user_identity_anomalies", "Protection required due to user identity anomalies"),
    PROTECTION_REQUIRED_CROSS_CORRELATION("protection_required_cross_correlation", "Protection required due to cross-correlation patterns"),
    PROTECTION_REQUIRED_ANALYZE_DATA("protection_required_analyze_data", "Protection required due to analyze data protection");
    
    private final String key;
    private final String description;
    
    /**
     * Get metric type by key
     * @param key the metric key
     * @return StatisticMetricType or null if not found
     */
    @JsonCreator
    public static StatisticMetricType fromKey(String key) {
        for (StatisticMetricType type : values()) {
            if (type.key.equalsIgnoreCase(key)) {
                return type;
            }
        }
        return null;
    }

    @JsonValue
    public String getKey() {
        return key;
    }
}
