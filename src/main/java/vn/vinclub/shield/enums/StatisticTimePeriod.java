package vn.vinclub.shield.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * Enum representing different time periods for statistics tracking
 */
@Getter
@RequiredArgsConstructor
public enum StatisticTimePeriod {
    
    ALL_TIME("all_time", "All time statistics", null),
    DAILY("daily", "Daily statistics", "yyyy-MM-dd")
    ;
    
    private final String key;
    private final String description;
    private final String dateFormat;
    
    /**
     * Generate time-based key suffix for the given time period
     * @param zoneId the timezone to use
     * @return time-based key suffix, empty string for ALL_TIME
     */
    public String generateTimeSuffix(ZoneId zoneId) {
        if (this == ALL_TIME) {
            return "";
        }
        
        LocalDateTime now = LocalDateTime.now(zoneId);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
        return ":" + formatter.format(now);
    }
    
    /**
     * Get time period by key
     * @param key the time period key
     * @return StatisticTimePeriod or null if not found
     */
    @JsonCreator
    public static StatisticTimePeriod fromKey(String key) {
        for (StatisticTimePeriod period : values()) {
            if (period.key.equalsIgnoreCase(key)) {
                return period;
            }
        }
        throw new IllegalArgumentException("Invalid period key: " + key);
    }

    @JsonValue
    public String getKey() {
        return key;
    }
}
