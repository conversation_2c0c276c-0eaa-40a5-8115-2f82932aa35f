package vn.vinclub.shield.enums;

import lombok.Getter;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

@Getter
public enum AppErrorCode {
    //--
    SUCCESS(0, "Thành công", "Success"),
    ERROR(-1, "Có lỗi xảy ra", "Error"),

    //--
    JSON_INVALID(8220500, "Sai định dạng JSON", "Invalid JSON format"),
    VALUE_INVALID(8220501, "Sai định dạng dữ liệu", "Invalid value"),
    BAD_REQUEST(8220502, "", "Bad request"),
    NOT_FOUND(8220503, "%s không tồn tại với %s = %s", "%s not found with %s = %s"),
    METHOD_NOT_ALLOWED(8220504, "Phương thức %s không hỗ trợ. Danh sách các phương thức hỗ trợ là: %s", "Method %s is not allowed. Supported methods are: %s"),
    INTERNAL_SERVER_ERROR(8220505, "Rất tiếc đã có lỗi xảy ra", "Internal server error"),
    RATE_LIMIT_EXCEED(8220506, "Người dùng gọi quá số lần sử dụng tối đa cho phép. Vui lòng thử lại sau", "Rate limit exceeded"),

    SYSTEM_ERROR(8220600, "Lỗi hệ thống", "System error"),
    INVALID_PARAM(8220601, "Tham số %s không hợp lệ", "Parameter %s is invalid"),
    DATA_IN_USE(8220602, "Dữ liệu đang được sử dụng, vui lòng thử lại", "Data is in use"),
    PARAM_REQUIRED(8220603, "Tham số %s là bắt buộc", "Parameter %s is required"),

    // Token verification errors
    TOKEN_SERVICE_NOT_FOUND(8220700, "Không tìm thấy dịch vụ xác thực token", "Token service not found"),
    TOKEN_INVALID(8220701, "Token không hợp lệ", "Invalid token"),
    TOKEN_USED(8220702, "Token đã được sử dụng", "Token has already been used"),
    TOKEN_REUSE_NOT_ALLOWED(8220703, "Token không thể được sử dụng lại cho hành động này", "Token reuse not allowed"),
    TOKEN_VERIFICATION_FAILED(8220704, "Xác thực token thất bại", "Token verification failed"),
    TOKEN_EXPIRED(8220705, "Token đã hết hạn", "Token has expired"),
    TOKEN_INVALID_TIME_RANGE(8220706, "Token không hợp lệ trong khoảng thời gian cho phép", "Invalid token time range"),

    // Shield errors
    SHIELD_TOKEN_INVALID(8220800, "Hệ thống tạm thời bị gián đoạn. Vui lòng thử lại hoặc liên hệ CSKH để được hỗ trợ.", "The system is temporarily unavailable. Please try again later or contact Customer Support for assistance."),
    SHIELD_PROTECT_REQUIRED(8220801, "Ứng dụng cần được cập nhật để tiếp tục sử dụng. Vui lòng nâng cấp lên phiên bản mới nhất.", "This version is no longer supported. Kindly update to the latest version to proceed."),
    ;

    private final Integer code;
    private final String message;
    private final String messageEn;

    AppErrorCode(Integer code, String message, String messageEn) {
        this.code = code;
        this.message = message;
        this.messageEn = messageEn;
    }

    public String getCurrentLocalMessage() {
        Locale currentLocale = LocaleContextHolder.getLocale();
        return currentLocale.getLanguage().equals("vi") ? message : messageEn;
    }

    // lookup table to be used to find enum for conversion
    private static final Map<Integer, AppErrorCode> lookup = new HashMap<>();

    static {
        for (AppErrorCode e : AppErrorCode.values())
            lookup.put(e.getCode(), e);
    }

    public static AppErrorCode lookupByErrorCode(Integer errorCode) {
        return lookup.get(errorCode);
    }

}
