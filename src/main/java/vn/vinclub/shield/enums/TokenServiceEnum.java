package vn.vinclub.shield.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import vn.vinclub.shield.constant.AppConst;

import java.util.Arrays;

@Getter
@RequiredArgsConstructor
public enum TokenServiceEnum {

    FIREBASE_APP_CHECK(AppConst.FIREBASE_APP_CHECK_TOKEN_HEADER_KEY, AppConst.FIREBASE_APP_CHECK_TOKEN_SERVICE);

    private final String tokenKey;
    private final String serviceName;

    public static TokenServiceEnum fromTokenHeaderKey(String tokenHeaderKey) {
        return Arrays.stream(TokenServiceEnum.values())
                .filter(tokenService -> tokenService.getTokenKey().equalsIgnoreCase(tokenHeaderKey))
                .findFirst()
                .orElse(null);
    }
}
