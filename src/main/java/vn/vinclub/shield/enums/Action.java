package vn.vinclub.shield.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Enum representing different actions that can be performed with a Firebase App Check token.
 * Each action can have a set of related actions that can reuse the same token.
 */
@Getter
@RequiredArgsConstructor
public enum Action {
    // Skip when action is unknown
    UNKNOWN("Unknown action"),

    // register actions
    REGISTER_CHECK("Check the phone/email register or not"),
    REGISTER_VERIFY("Verify the phone/email register"),
    REGISTER_CONFIRM("Confirm and register a new account"),
    REGISTER_ONBOARDING("Onboarding a new account"),
    ALL_REGISTER("All action for register"),

    // log in flow action
    LOGIN("Login to the application"),
    ALL_LOGIN("All action for login"),

    // reset password actions
    RESET_PASSWORD_CHECK("Check the phone/email reset password"),
    RESET_PASSWORD_VERIFY("Verify the phone/email reset password"),
    RESET_PASSWORD_CONFIRM("Confirm and reset password"),
    ALL_RESET_PASSWORD("All action for reset password"),

    // VBD SDK action
    VBD_SDK_REQUEST_TOKEN("Request token for VBD SDK"),
    ALL_VBD_SDK("All action for VBD SDK"),


    // voucher actions
    VALIDATE_VOUCHER("Validate a voucher"),
    EXCHANGE_VOUCHER("Exchange a voucher"),
    REDEEM_VOUCHER("Redeem a voucher"),
    RESERVE_VOUCHER("Reserve a voucher"),
    ALL_VOUCHER("All action for voucher"),

    // game actions
    PLAY_GAME("Play a game"),
    ALL_GAME("All action for game"),

    // transfer point actions
    TRANSFER_POINT("Transfer point to another account"),
    ALL_TRANSFER_POINT("All action for transfer point"),

    // topup point actions
    TOPUP_POINT("Topup point to an account"),
    ALL_TOPUP_POINT("All action for topup point"),
    ;

    private final String description;

    // Map to store which actions can reuse tokens from other actions
    private static final Map<Action, Set<Action>> TOKEN_REUSE_RULES = new HashMap<>();

    static {
        // Define token reuse rules for register flow
        TOKEN_REUSE_RULES.put(REGISTER_VERIFY, Set.of(REGISTER_CHECK));
        TOKEN_REUSE_RULES.put(REGISTER_CONFIRM, Set.of(REGISTER_VERIFY, REGISTER_CHECK));
        TOKEN_REUSE_RULES.put(REGISTER_ONBOARDING, Set.of(REGISTER_VERIFY, REGISTER_CHECK));

        // Define token reuse rules for login flow
        TOKEN_REUSE_RULES.put(LOGIN, Set.of(REGISTER_CHECK));

        // Define token reuse rules for reset password flow
        TOKEN_REUSE_RULES.put(RESET_PASSWORD_VERIFY, Set.of(RESET_PASSWORD_CHECK));
        TOKEN_REUSE_RULES.put(RESET_PASSWORD_CONFIRM, Set.of(RESET_PASSWORD_VERIFY, RESET_PASSWORD_CHECK));

        // Add more rules as needed
    }

    /**
     * Check if this action can reuse a token from the previous action.
     *
     * @param previousAction The previous action that used the token
     * @return true if this action can reuse the token from the previous action, false otherwise
     */
    public boolean canReuseTokenFrom(Action previousAction) {
        if (this == previousAction) {
            return false; // Cannot reuse token for the same action
        }
        
        Set<Action> allowedPreviousActions = TOKEN_REUSE_RULES.get(this);
        return allowedPreviousActions != null && allowedPreviousActions.contains(previousAction);
    }

    /**
     * Get a TokenAction by its name.
     *
     * @param name The name of the action
     * @return The TokenAction enum value, or null if not found
     */
    @JsonCreator
    public static Action fromString(String name) {
        return Arrays.stream(Action.values())
                .filter(action -> action.name().equalsIgnoreCase(name))
                .findFirst()
                .orElse(UNKNOWN);
    }
}