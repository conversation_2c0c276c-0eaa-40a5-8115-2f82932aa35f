package vn.vinclub.shield.controller.internal;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.shield.dto.request.ApiProtectRequest;
import vn.vinclub.shield.dto.response.ApiProtectResponse;
import vn.vinclub.shield.dto.response.ShieldStatisticsResponse;
import vn.vinclub.shield.enums.StatisticTimePeriod;
import vn.vinclub.shield.service.ShieldService;
import vn.vinclub.shield.util.ServiceResponse;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/internal")
@RequiredArgsConstructor
public class ShieldInternalController {

    private final ShieldService shieldService;

    @PostMapping("/protect-api")
    public ServiceResponse<ApiProtectResponse> protectApi(@RequestBody ApiProtectRequest request) {
        try (var p = new Profiler(getClass(), "protectApi")) {
            return ServiceResponse.success(shieldService.protectApi(request));
        }
    }

    @GetMapping("/stats/{timePeriod}")
    public ServiceResponse<ShieldStatisticsResponse> getStatistics(@PathVariable String timePeriod) {
        try (var p = new Profiler(getClass(), "getStatistics")) {
            return ServiceResponse.success(shieldService.getStatistics(StatisticTimePeriod.fromKey(timePeriod)));
        }
    }

    @DeleteMapping("/stats/{timePeriod}")
    public ServiceResponse<Boolean> resetStatistics(@PathVariable String timePeriod) {
        try (var p = new Profiler(getClass(), "resetStatistics")) {
            return ServiceResponse.success(shieldService.resetStatistics(StatisticTimePeriod.fromKey(timePeriod)));
        }
    }
}

