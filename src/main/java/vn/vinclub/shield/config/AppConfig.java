package vn.vinclub.shield.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.shield.constant.AppConst;
import vn.vinclub.shield.interceptor.RestTemplateLoggingInterceptor;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

@Configuration
@EnableRetry
@RequiredArgsConstructor
public class AppConfig {

    @Value("${default.request.timeout:45000}")
    private int requestTimeout;

    @Value("${vinclub.logging.level.client.RestTemplate:}")
    private String restTemplateLogLevel;

    private final ObjectMapper mapper;

    @Bean
    public RestTemplate restTemplate() {

        var requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectTimeout(requestTimeout);
        requestFactory.setConnectionRequestTimeout(requestTimeout);

        RestTemplate restTemplate = new RestTemplate();
        if ("DEBUG".equalsIgnoreCase(restTemplateLogLevel)) {

            restTemplate.setRequestFactory(new BufferingClientHttpRequestFactory(requestFactory));

            List<ClientHttpRequestInterceptor> interceptors = restTemplate.getInterceptors();
            if (CollectionUtils.isEmpty(interceptors)) {
                interceptors = new ArrayList<>();
            }
            interceptors.add(new RestTemplateLoggingInterceptor());

            restTemplate.setInterceptors(interceptors);

        } else {
            restTemplate.setRequestFactory(requestFactory);
        }

        return restTemplate;
    }

    @Bean
    public BaseJsonUtils defaultBaseJsonUtils() {
        return new BaseJsonUtils(mapper);
    }

    @Bean
    public LocaleResolver localeResolver() {
        AcceptHeaderLocaleResolver resolver = new AcceptHeaderLocaleResolver();
        resolver.setDefaultLocale(Locale.of(AppConst.DEFAULT_LANGUAGE, AppConst.DEFAULT_COUNTRY));
        return resolver;
    }

}
