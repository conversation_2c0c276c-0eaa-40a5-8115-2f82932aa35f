package vn.vinclub.shield.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Configuration properties for RequestCounterService
 * Externalizes all hardcoded values for better maintainability and environment-specific configuration
 */
@Data
@Component
@ConfigurationProperties(prefix = "shield.analytics")
public class ShieldAnalyticsConfigs {

    /**
     * Timezone for time-based operations
     */
    private String timezone = "GMT+7";

    /**
     * Enable statistics tracking
     */
    private boolean statsEnabled = true;

    /**
     * TTL for daily statistics cache in hours
     */
    private int statsDailyTtlHours = 72; // 3 days

    /**
     * Velocity detection configuration
     */
    private VelocityConfig velocity = new VelocityConfig();

    /**
     * Rapid fire detection configuration
     */
    private RapidFireConfig rapidFire = new RapidFireConfig();

    /**
     * New indicators detection configuration
     */
    private NewIndicatorsConfig newIndicators = new NewIndicatorsConfig();

    /**
     * Off-hours activity detection configuration
     */
    private OffHoursConfig offHours = new OffHoursConfig();

    /**
     * Automated behavior detection configuration
     */
    private AutomatedBehaviorConfig automatedBehavior = new AutomatedBehaviorConfig();

    /**
     * Unusual user activity detection configuration
     */
    private UnusualActivityConfig unusualActivity = new UnusualActivityConfig();

    @Data
    public static class VelocityConfig {
        /**
         * TTL for velocity counter cache in minutes
         */
        private int ttlMinutes = 60;

        /**
         * Threshold for high-frequency pattern detection
         */
        private int threshold = 10;
    }

    @Data
    public static class RapidFireConfig {
        /**
         * TTL for rapid fire detection cache in minutes
         */
        private int ttlMinutes = 5;

        /**
         * Threshold for rapid fire pattern detection (requests per minute)
         */
        private int threshold = 5;
    }

    @Data
    public static class NewIndicatorsConfig {
        /**
         * TTL for new indicators cache in hours
         */
        private int ttlHours = 1;

        /**
         * Threshold for multiple new indicators detection
         */
        private int threshold = 3;
    }

    @Data
    public static class OffHoursConfig {
        /**
         * Start hour for off-hours period (24-hour format)
         */
        private int startHour = 0;

        /**
         * End hour for off-hours period (24-hour format)
         */
        private int endHour = 5;
    }

    @Data
    public static class AutomatedBehaviorConfig {
        /**
         * TTL for automated behavior cache in minutes
         */
        private int ttlMinutes = 30;

        /**
         * Analysis window for automated behavior detection in minutes
         */
        private int analysisWindowMinutes = 5;

        /**
         * Threshold for automated behavior detection (requests per analysis window)
         */
        private int threshold = 15;
    }

    @Data
    public static class UnusualActivityConfig {
        /**
         * TTL for unusual activity cache in hours
         */
        private int ttlHours = 24;

        /**
         * Threshold for unusual activity detection (active hours per day)
         */
        private int threshold = 12;
    }
}
