package vn.vinclub.shield.config;

import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import vn.vinclub.shield.util.RSAKeyUtils;

import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;

/**
 * Configuration class for Shield Token generation and validation
 */
@Slf4j
@Configuration
@Getter
public class ShieldTokenConfig {

    @Value("${shield.token.issuer}")
    private String issuer;

    @Value("${shield.token.audience}")
    private String audience;

    @Value("${shield.token.expiration-minutes}")
    private int expirationMinutes;

    @Value("${shield.token.private-key:}")
    private String privateKeyPem;

    @Value("${shield.token.public-key:}")
    private String publicKeyPem;

    private PrivateKey privateKey;
    private PublicKey publicKey;

    @PostConstruct
    public void init() {
        log.info("Initializing Shield Token configuration...");
        log.info("Shield Token Issuer: {}", issuer);
        log.info("Shield Token Audience: {}", audience);
        log.info("Shield Token Expiration: {} minutes", expirationMinutes);

        initializeKeys();
        
        log.info("Shield Token configuration initialized successfully");
    }

    /**
     * Initialize RSA key pair for token signing and verification
     */
    private void initializeKeys() {
        if (StringUtils.hasText(privateKeyPem)) {
            // Load private key from configuration
            try {
                log.info("Loading RSA private key from configuration");
                privateKey = RSAKeyUtils.loadPrivateKey(privateKeyPem);
                publicKey = RSAKeyUtils.loadPublicKey(publicKeyPem);
                log.info("Successfully loaded RSA private key from configuration");
            } catch (Exception e) {
                log.error("Failed to load private key from configuration, generating new key pair", e);
                generateNewKeyPair();
            }
        } else {
            // Generate new key pair
            log.warn("No private key configured, generating new RSA key pair");
            generateNewKeyPair();
        }
    }

    /**
     * Generate a new RSA key pair
     */
    private void generateNewKeyPair() {
        try {
            KeyPair keyPair = RSAKeyUtils.generateKeyPair();
            privateKey = keyPair.getPrivate();
            publicKey = keyPair.getPublic();
            
            // Log the generated keys for configuration (in non-production environments)
            String generatedPrivateKeyPem = RSAKeyUtils.privateKeyToPem(privateKey);
            String generatedPublicKeyPem = RSAKeyUtils.publicKeyToPem(publicKey);
            
            log.info("Generated new RSA key pair for Shield Token signing");
            log.info("Private Key (for signing): \n{}", generatedPrivateKeyPem);
            log.debug("Public Key (for verification):\n{}", generatedPublicKeyPem);
            
        } catch (Exception e) {
            log.error("Failed to generate RSA key pair", e);
            throw new RuntimeException("Failed to initialize Shield Token keys", e);
        }
    }

    /**
     * Get token expiration time in seconds
     *
     * @return expiration time in seconds
     */
    public long getExpirationSeconds() {
        return expirationMinutes * 60L;
    }
}
