package vn.vinclub.shield.service.impl;

import com.auth0.jwk.Jwk;
import com.auth0.jwk.JwkException;
import com.auth0.jwk.JwkProvider;
import com.auth0.jwk.JwkProviderBuilder;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.DecodedJWT;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.shield.constant.AppConst;
import vn.vinclub.shield.dto.TokenVerifyResult;
import vn.vinclub.shield.enums.Action;
import vn.vinclub.shield.enums.AppErrorCode;
import vn.vinclub.shield.model.TokenUsageRecord;
import vn.vinclub.shield.repository.TokenUsageRepository;
import vn.vinclub.shield.service.TokenService;

import java.net.MalformedURLException;
import java.net.URL;
import java.security.interfaces.RSAPublicKey;
import java.time.Instant;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service(AppConst.FIREBASE_APP_CHECK_TOKEN_SERVICE)
@RequiredArgsConstructor
public class FireBaseAppCheckTokenServiceImpl implements TokenService {

    private final TokenUsageRepository tokenUsageRepository;

    @Value("${firebase.app-check.project.number}")
    private String projectNumber;

    @Value("${firebase.app-check.project.app_id}")
    private String appId;

    @Value("${firebase.app-check.project.jwks}")
    private String jwks;

    private static final String FIREBASE_APP_CHECK_ISSUER_PREFIX = "https://firebaseappcheck.googleapis.com/";
    private static final String FIREBASE_APP_CHECK_AUDIENCE_PREFIX = "projects/";
    private static final String EXPECTED_ALGORITHM = "RS256";
    private static final String EXPECTED_TYPE = "JWT";

    private JwkProvider jwkProvider;

    @PostConstruct
    public void init() {
        try (var p = new Profiler(getClass(), "init")) {
            // Initialize the JWK provider with caching
            JwkProviderBuilder providerBuilder = new JwkProviderBuilder(new URL(jwks));
            jwkProvider = providerBuilder
                    .cached(10, 6, TimeUnit.HOURS)  // Cache up to 10 JWKs for 6 hours
                    .build();
            log.info("JWK provider initialized with URL: {}", jwks);
        } catch (MalformedURLException e) {
            log.error("Failed to initialize JWK provider", e);
            throw new RuntimeException("Failed to initialize JWK provider", e);
        }
    }

    @Override
    public TokenVerifyResult verifyToken(Action action, String key, String token, Instant validFrom, Instant validTo) {
        try (var p = new Profiler(getClass(), "verifyToken")) {
            log.info("Verifying token for action: {}, key: {}", action, key);

            if (StringUtils.isBlank(token)) {
                log.error("Token is blank");
                return TokenVerifyResult.invalid(AppErrorCode.TOKEN_INVALID);
            }

            // Extract token ID and verify token
            String tokenId;
            // Default TTL in case of exceptions
            long tokenUsageTtlSeconds = 86400L; // 24-hour default
            try {
                // Decode the token without verification to extract the key ID
                DecodedJWT jwt = JWT.decode(token);

                // 1. Verify the token's header uses the algorithm RS256
                if (!EXPECTED_ALGORITHM.equals(jwt.getAlgorithm())) {
                    log.error("Token algorithm mismatch. Expected: {}, Actual: {}", EXPECTED_ALGORITHM, jwt.getAlgorithm());
                    return TokenVerifyResult.invalid(AppErrorCode.TOKEN_INVALID);
                }

                // 2. Verify the token's header has type JWT
                if (!EXPECTED_TYPE.equals(jwt.getType())) {
                    log.error("Token type mismatch. Expected: {}, Actual: {}", EXPECTED_TYPE, jwt.getType());
                    return TokenVerifyResult.invalid(AppErrorCode.TOKEN_INVALID);
                }

                // Get the key ID from the token header
                String keyId = jwt.getKeyId();
                if (StringUtils.isBlank(keyId)) {
                    log.error("Token key ID is missing");
                    return TokenVerifyResult.invalid(AppErrorCode.TOKEN_INVALID);
                }

                // Get the public key from the JWK provider
                Jwk jwk = jwkProvider.get(keyId);
                RSAPublicKey publicKey = (RSAPublicKey) jwk.getPublicKey();

                // Create a verifier with the public key
                Algorithm algorithm = Algorithm.RSA256(publicKey, null);
                JWTVerifier verifier = JWT.require(algorithm)
                        // 3. Ensure the token is issued by Firebase App Check
                        .withIssuer(FIREBASE_APP_CHECK_ISSUER_PREFIX + projectNumber)
                        // 4. Ensure the token's audience matches your project
                        .withAudience(FIREBASE_APP_CHECK_AUDIENCE_PREFIX + projectNumber, FIREBASE_APP_CHECK_AUDIENCE_PREFIX + appId)
                        .build();

                // Verify the token
                jwt = verifier.verify(token);

                // 5. Ensure the token has not expired
                if (jwt.getExpiresAt().toInstant().isBefore(Instant.now())) {
                    log.error("Token has expired");
                    return TokenVerifyResult.invalid(AppErrorCode.TOKEN_INVALID);
                }

                // 6. Ensure the issued time is within the valid time range
                if (jwt.getIssuedAt().toInstant().isBefore(validFrom) || jwt.getIssuedAt().toInstant().isAfter(validTo)) {
                    log.error("Token issued time is outside the valid range");
                    return TokenVerifyResult.invalid(AppErrorCode.TOKEN_INVALID_TIME_RANGE);
                }

                // Calculate TTL based on token expiry time + 5-minute buffer
                Instant expiryTime = jwt.getExpiresAt().toInstant();
                Instant now = Instant.now();
                // Calculate the remaining time until token expiry and add 5 minutes (300 seconds) buffer;
                // This ensures the token usage record remains in Redis for a short time after token expiration
                tokenUsageTtlSeconds = expiryTime.getEpochSecond() - now.getEpochSecond() + 300;
                log.debug("Token usage TTL calculated: {} seconds", tokenUsageTtlSeconds);

                // The token's subject will be the app ID
                tokenId = jwt.getId();

            } catch (TokenExpiredException e) {
                log.error("Token has expired: {}", e.getMessage());
                return TokenVerifyResult.invalid(AppErrorCode.TOKEN_EXPIRED);
            } catch (JWTVerificationException e) {
                log.error("Token verification failed - request: {}", e.getMessage());
                return TokenVerifyResult.invalid(AppErrorCode.TOKEN_VERIFICATION_FAILED);
            } catch (JwkException e) {
                log.error("Failed to retrieve JWK: {}", e.getMessage());
                return TokenVerifyResult.invalid(AppErrorCode.TOKEN_VERIFICATION_FAILED);
            } catch (Exception e) {
                log.error("Unexpected error during token verification: {}", e.getMessage());
                return TokenVerifyResult.invalid(AppErrorCode.TOKEN_VERIFICATION_FAILED);
            }

            // Check if a token has been used before
            TokenUsageRecord existingRecord = getTokenUsageRecord(tokenId);

            if (existingRecord != null) {
                // Check if a token is being used by the same key
                if (action.equals(existingRecord.getAction()) && Objects.equals(existingRecord.getKey(), token)) {
                    log.warn("Token has already been used for the same action and key");
                    return TokenVerifyResult.invalid(AppErrorCode.TOKEN_USED);
                }

                // Check if the token can be reused for this action
                if (!canReuseToken(existingRecord, action)) {
                    log.warn("Token reuse not allowed for action: {}", action);
                    return TokenVerifyResult.invalid(AppErrorCode.TOKEN_REUSE_NOT_ALLOWED);
                }

                // Update token usage record
                existingRecord.recordUsage(action);
                saveTokenUsageRecord(existingRecord, tokenUsageTtlSeconds);

            } else {
                // Create and save token usage record
                TokenUsageRecord newRecord = TokenUsageRecord.createNew(tokenId, key, action);
                saveTokenUsageRecord(newRecord, tokenUsageTtlSeconds);
            }

            return TokenVerifyResult.valid();
        }
    }

    @Override
    public TokenUsageRecord getTokenUsageRecord(String tokenId) {
        try (var p = new Profiler(getClass(), "getTokenUsageRecord")) {
            return tokenUsageRepository.findByTokenId(tokenId);
        }
    }

    @Override
    public void saveTokenUsageRecord(TokenUsageRecord record, long tokenUsageTtlSeconds) {
        try (var p = new Profiler(getClass(), "saveTokenUsageRecord")) {
            tokenUsageRepository.save(record, tokenUsageTtlSeconds);
        }
    }

    @Override
    public boolean canReuseToken(TokenUsageRecord record, Action newAction) {
        try (var p = new Profiler(getClass(), "canReuseToken")) {
            return newAction.canReuseTokenFrom(record.getAction());
        }
    }
}
