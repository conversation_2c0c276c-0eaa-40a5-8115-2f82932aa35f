package vn.vinclub.shield.service;

import vn.vinclub.shield.dto.TokenVerifyResult;
import vn.vinclub.shield.enums.Action;
import vn.vinclub.shield.model.TokenUsageRecord;

import java.time.Instant;

public interface TokenService {

    /**
     * Verifies a token for a specific action and key.
     * This method performs validation of the token, checks if it has been used before,
     * and determines if it can be reused for the requested action.
     *
     * @param action    The action to verify
     * @param key       The key to verify
     * @param token     The token to verify
     * @param validFrom The valid from time of the token
     * @param validTo   The valid to time of the token
     * @return A response indicating whether the token is valid for the requested action
     */
    TokenVerifyResult verifyToken(Action action, String key, String token, Instant validFrom, Instant validTo);

    /**
     * Retrieves a token usage record by its token ID.
     *
     * @param tokenId The unique identifier of the token
     * @return The token usage record if found, null otherwise
     */
    TokenUsageRecord getTokenUsageRecord(String tokenId);

    /**
     * Saves a token usage record with the specified time-to-live.
     *
     * @param record               The token usage record to save
     * @param tokenUsageTtlSeconds The time-to-live in seconds for the token usage record in Redis.
     *                             Typically calculated as token expiration time plus a buffer period (e.g., 5 minutes)
     */
    void saveTokenUsageRecord(TokenUsageRecord record, long tokenUsageTtlSeconds);

    /**
     * Checks if a token can be reused for a new action based on token reuse rules.
     *
     * @param record    The existing token usage record
     * @param newAction The new action to be performed with the token
     * @return true if the token can be reused for the new action, false otherwise
     */
    boolean canReuseToken(TokenUsageRecord record, Action newAction);
}
