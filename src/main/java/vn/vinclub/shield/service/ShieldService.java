package vn.vinclub.shield.service;

import vn.vinclub.shield.dto.request.ApiProtectRequest;
import vn.vinclub.shield.dto.response.ApiProtectResponse;
import vn.vinclub.shield.dto.response.ShieldStatisticsResponse;
import vn.vinclub.shield.enums.StatisticTimePeriod;

public interface ShieldService {
    ApiProtectResponse protectApi(ApiProtectRequest request);

    ShieldStatisticsResponse getStatistics(StatisticTimePeriod timePeriod);

    Boolean resetStatistics(StatisticTimePeriod timePeriod);
}
