package vn.vinclub.shield.service;

import vn.vinclub.shield.dto.EvaluationData;
import vn.vinclub.shield.dto.ShieldEvaluationResult;
import vn.vinclub.shield.dto.ShieldTokenValidationResult;

public interface ShieldEvaluationService {

    ShieldEvaluationResult evaluate(EvaluationData evaluationData);

    ShieldTokenValidationResult validateShieldToken(EvaluationData evaluationData, String shieldToken);
}
