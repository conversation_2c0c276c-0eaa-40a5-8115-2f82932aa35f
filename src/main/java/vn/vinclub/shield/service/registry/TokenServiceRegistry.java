package vn.vinclub.shield.service.registry;

import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import vn.vinclub.shield.enums.AppErrorCode;
import vn.vinclub.shield.enums.TokenServiceEnum;
import vn.vinclub.shield.exception.BusinessLogicException;
import vn.vinclub.shield.service.TokenService;

import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
public class TokenServiceRegistry {
    private final Map<String, TokenService> tokenServices;

    public TokenService getService(@NotBlank String tokenKey) {
        var tokenService = TokenServiceEnum.fromTokenHeaderKey(tokenKey);

        if (tokenService == null) {
            log.error("Token service not found for token header key: {}", tokenKey);
            throw new BusinessLogicException(AppErrorCode.TOKEN_SERVICE_NOT_FOUND);
        }

        return tokenServices.get(tokenService.getServiceName());
    }
}
