package vn.vinclub.shield.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.shield.dto.RiskAssessment;
import vn.vinclub.shield.dto.TrackingContext;
import vn.vinclub.shield.dto.TrackingResults;

import java.util.ArrayList;
import java.util.List;

/**
 * Service for comprehensive risk assessment based on tracking data
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RiskAssessmentService {

    private final TrackingRequestService trackingRequestService;

    /**
     * Perform comprehensive risk assessment using boolean fraud indicators
     */
    @Profiler
    public RiskAssessment assessRisk(TrackingContext context) {
        log.debug("[START] action={}, hasCustomer={}, hasDevice={}, hasIP={}",
                context.getAction(), context.hasCustomerData(), context.hasDeviceData(), context.hasIpAddress());

        // Get tracking results
        TrackingResults trackingResults = trackingRequestService.analyzeTrackingRequest(context);

        // Identify fraud indicators
        List<RiskAssessment.RiskFactor> riskFactors = new ArrayList<>();
        identifyFraudIndicators(trackingResults, riskFactors);

        // Determine if protection is required based on boolean logic
        boolean requiresProtection = !riskFactors.isEmpty();

        RiskAssessment assessment = RiskAssessment.builder()
                .numberOfRisks(trackingResults.getNumberOfRisks())
                .requiresProtection(requiresProtection)
                .riskFactors(riskFactors)
                .trackingResults(trackingResults)
                .build();

        log.info("[COMPLETE] action={}, protection={}, factors={} => {}",
                context.getAction(), requiresProtection, riskFactors.size(),
                trackingResults.getFraudRiskSummary());

        return assessment;
    }

    /**
     * Identify fraud indicators using boolean logic
     */
    private void identifyFraudIndicators(TrackingResults results, List<RiskAssessment.RiskFactor> riskFactors) {
        // Network-based fraud indicators
        if (results.hasNetworkFraudIndicators()) {
            riskFactors.add(RiskAssessment.RiskFactor.of(
                    "NEW_IP", "IP address is completely new to the system"));
        }

        // User identity fraud indicators
        if (results.hasUserIdentityFraudIndicators()) {
            riskFactors.add(RiskAssessment.RiskFactor.of(
                    "NEW_USER_IDENTITY", "User identity indicators detected"));
        }

        // Device fraud indicators
        if (results.hasDeviceFraudIndicators()) {
            riskFactors.add(RiskAssessment.RiskFactor.of(
                    "NEW_DEVICE", "Device fingerprint indicators detected"));
        }



        // Behavioral anomalies
        if (results.hasBehavioralAnomalies()) {
            if (results.isBehaviorNewUserFromKnownNetwork()) {
                riskFactors.add(RiskAssessment.RiskFactor.of(
                        "NEW_USER_KNOWN_NETWORK", "New user from previously seen network"));
            }
            if (results.isBehaviorKnownUserFromNewNetwork()) {
                riskFactors.add(RiskAssessment.RiskFactor.of(
                        "KNOWN_USER_NEW_NETWORK", "Known user from new network"));
            }
            if (results.isBehaviorNewDeviceFromKnownNetwork()) {
                riskFactors.add(RiskAssessment.RiskFactor.of(
                        "NEW_DEVICE_KNOWN_NETWORK", "New device from known network"));
            }
            if (results.isBehaviorKnownDeviceFromNewNetwork()) {
                riskFactors.add(RiskAssessment.RiskFactor.of(
                        "KNOWN_DEVICE_NEW_NETWORK", "Known device from new network"));
            }
            if (results.isBehaviorNewUserFromKnownDevice()) {
                riskFactors.add(RiskAssessment.RiskFactor.of(
                        "NEW_USER_KNOWN_DEVICE", "New user from known device"));
            }
            if (results.isBehaviorKnownUserFromNewDevice()) {
                riskFactors.add(RiskAssessment.RiskFactor.of(
                        "KNOWN_USER_NEW_DEVICE", "Known user from new device"));
            }
        }

        // Velocity fraud indicators
        if (results.hasVelocityFraudIndicators()) {
            if (results.isVelocityMultipleNewIndicatorsDetected()) {
                riskFactors.add(RiskAssessment.RiskFactor.of(
                        "VELOCITY_MULTIPLE_NEW", "Multiple new indicators detected rapidly"));
            }
            if (results.isVelocityHighFrequencyFromNetwork()) {
                riskFactors.add(RiskAssessment.RiskFactor.of(
                        "HIGH_FREQUENCY_NETWORK", "High frequency requests from network"));
            }
            if (results.isVelocityUnusualUserActivityPattern()) {
                riskFactors.add(RiskAssessment.RiskFactor.of(
                        "UNUSUAL_USER_ACTIVITY", "Unusual user activity pattern detected"));
            }
        }

        // Temporal anomalies
        if (results.hasTemporalAnomalies()) {
            if (results.isTemporalOffHoursActivityDetected()) {
                riskFactors.add(RiskAssessment.RiskFactor.of(
                        "OFF_HOURS_ACTIVITY", "Activity detected outside normal hours"));
            }
            if (results.isTemporalAutomatedBehaviorPattern()) {
                riskFactors.add(RiskAssessment.RiskFactor.of(
                        "AUTOMATED_BEHAVIOR", "Automated behavior pattern detected"));
            }
            if (results.isTemporalRapidFireRequestPattern()) {
                riskFactors.add(RiskAssessment.RiskFactor.of(
                        "RAPID_FIRE_REQUESTS", "Rapid-fire request pattern detected"));
            }
        }
    }
}
