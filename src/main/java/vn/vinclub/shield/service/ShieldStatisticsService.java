package vn.vinclub.shield.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.springframework.stereotype.Service;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.shield.config.ShieldAnalyticsConfigs;
import vn.vinclub.shield.constant.AppConst;
import vn.vinclub.shield.dto.response.ShieldStatisticsResponse;
import vn.vinclub.shield.enums.StatisticMetricType;
import vn.vinclub.shield.enums.StatisticTimePeriod;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;
import java.util.Set;

/**
 * Service for tracking and managing shield protection statistics
 * Uses Redis hash for atomic operations and efficient storage
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShieldStatisticsService {
    
    private final RedissonClient redissonClient;
    private final ShieldAnalyticsConfigs configs;

    private final Codec mapStringLongCodec = AppConst.CacheCodec.STRING_LONG_CODEC;
    
    /**
     * Increment a specific metric counter atomically
     * @param metricType the metric to increment
     * @param incrementBy the amount to increment (default 1)
     */
    @Profiler
    public void incrementMetric(StatisticMetricType metricType, long incrementBy) {
        if (!configs.isStatsEnabled()) {
            return;
        }
        
        try {
            ZoneId zoneId = ZoneId.of(configs.getTimezone());
            
            // Increment for all time periods
            for (StatisticTimePeriod period : StatisticTimePeriod.values()) {
                String redisKey = buildRedisKey(period, zoneId);
                RMap<String, Long> statsMap = redissonClient.getMap(redisKey, mapStringLongCodec);
                
                // Atomic increment operation
                statsMap.addAndGetAsync(metricType.getKey(), incrementBy);
                
                // Set TTL for time-based statistics
                setTtlIfNeeded(statsMap, period);
            }
            
            log.debug("Incremented metric {} by {}", metricType.getKey(), incrementBy);
        } catch (Exception e) {
            log.error("Failed to increment metric {}: {}", metricType.getKey(), e.getMessage(), e);
        }
    }
    
    /**
     * Increment a metric by 1
     * @param metricType the metric to increment
     */
    @Profiler
    public void incrementMetric(StatisticMetricType metricType) {
        incrementMetric(metricType, 1L);
    }
    
    /**
     * Get statistics for a specific time period
     * @param timePeriod the time period to retrieve statistics for
     * @return ShieldStatisticsResponse containing the statistics
     */
    @Profiler
    public ShieldStatisticsResponse getStatistics(StatisticTimePeriod timePeriod) {
        try {
            ZoneId zoneId = ZoneId.of(configs.getTimezone());
            String redisKey = buildRedisKey(timePeriod, zoneId);
            RMap<String, Long> statsMap = redissonClient.getMap(redisKey, mapStringLongCodec);
            
            Set<String> allKeys = statsMap.readAllKeySet();
            Map<String, Long> allMetrics = statsMap.getAll(allKeys);
            
            return ShieldStatisticsResponse.builder()
                    .timePeriod(timePeriod)
                    .retrievedAt(LocalDateTime.now(zoneId))
                    .coreMetrics(buildCoreMetrics(allMetrics))
                    .riskCategoryMetrics(buildRiskCategoryMetrics(allMetrics))
                    .build();
                    
        } catch (Exception e) {
            log.error("Failed to retrieve statistics for period {}: {}", timePeriod, e.getMessage(), e);
            return ShieldStatisticsResponse.builder()
                    .timePeriod(timePeriod)
                    .retrievedAt(LocalDateTime.now(ZoneId.of(configs.getTimezone())))
                    .coreMetrics(new ShieldStatisticsResponse.CoreMetrics())
                    .riskCategoryMetrics(new ShieldStatisticsResponse.RiskCategoryMetrics())
                    .build();
        }
    }
    
    /**
     * Reset statistics for a specific time period
     * @param timePeriod the time period to reset
     * @return true if reset was successful
     */
    @Profiler
    public boolean resetStatistics(StatisticTimePeriod timePeriod) {
        if (!configs.isStatsEnabled()) {
            return false;
        }
        
        try {
            ZoneId zoneId = ZoneId.of(configs.getTimezone());
            String redisKey = buildRedisKey(timePeriod, zoneId);
            RMap<String, Long> statsMap = redissonClient.getMap(redisKey, mapStringLongCodec);
            
            statsMap.clear();
            log.info("Reset statistics for period: {}", timePeriod);
            return true;
            
        } catch (Exception e) {
            log.error("Failed to reset statistics for period {}: {}", timePeriod, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Build Redis key for the given time period
     */
    private String buildRedisKey(StatisticTimePeriod timePeriod, ZoneId zoneId) {
        String timeSuffix = timePeriod.generateTimeSuffix(zoneId);
        return "shield_svc_stats:" + timePeriod.getKey() + timeSuffix;
    }
    
    /**
     * Set TTL for time-based statistics
     */
    private void setTtlIfNeeded(RMap<String, Long> statsMap, StatisticTimePeriod period) {
        switch (period) {
            case DAILY:
                statsMap.expire(Duration.ofHours(configs.getStatsDailyTtlHours()));
                break;
            case ALL_TIME:
                // No TTL for all-time statistics
                break;
        }
    }
    
    /**
     * Build core metrics from raw metrics map
     */
    private ShieldStatisticsResponse.CoreMetrics buildCoreMetrics(Map<String, Long> allMetrics) {
        return ShieldStatisticsResponse.CoreMetrics.builder()
                .evaluationPass(allMetrics.get(StatisticMetricType.EVALUATION_PASS.getKey()))
                .protectionRequired(allMetrics.get(StatisticMetricType.PROTECTION_REQUIRED.getKey()))
                .shieldVerificationSuccess(allMetrics.get(StatisticMetricType.SHIELD_VERIFICATION_SUCCESS.getKey()))
                .shieldVerificationFailed(allMetrics.get(StatisticMetricType.SHIELD_VERIFICATION_FAILED.getKey()))
                .build();
    }
    
    /**
     * Build risk category metrics from raw metrics map
     */
    private ShieldStatisticsResponse.RiskCategoryMetrics buildRiskCategoryMetrics(Map<String, Long> allMetrics) {
        return ShieldStatisticsResponse.RiskCategoryMetrics.builder()
                .fraudDetection(allMetrics.get(StatisticMetricType.PROTECTION_REQUIRED_FRAUD_DETECTION.getKey()))
                .velocityLimits(allMetrics.get(StatisticMetricType.PROTECTION_REQUIRED_VELOCITY_LIMITS.getKey()))
                .behavioralAnomalies(allMetrics.get(StatisticMetricType.PROTECTION_REQUIRED_BEHAVIORAL_ANOMALIES.getKey()))
                .networkAnomalies(allMetrics.get(StatisticMetricType.PROTECTION_REQUIRED_NETWORK_ANOMALIES.getKey()))
                .deviceAnomalies(allMetrics.get(StatisticMetricType.PROTECTION_REQUIRED_DEVICE_ANOMALIES.getKey()))
                .temporalAnomalies(allMetrics.get(StatisticMetricType.PROTECTION_REQUIRED_TEMPORAL_ANOMALIES.getKey()))
                .userIdentityAnomalies(allMetrics.get(StatisticMetricType.PROTECTION_REQUIRED_USER_IDENTITY_ANOMALIES.getKey()))
                .crossCorrelation(allMetrics.get(StatisticMetricType.PROTECTION_REQUIRED_CROSS_CORRELATION.getKey()))
                .analyzeData(allMetrics.get(StatisticMetricType.PROTECTION_REQUIRED_ANALYZE_DATA.getKey()))
                .build();
    }
}
