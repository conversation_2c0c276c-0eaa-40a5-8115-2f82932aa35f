package vn.vinclub.shield.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.shield.dto.TrackingContext;
import vn.vinclub.shield.dto.TrackingResults;
import vn.vinclub.shield.enums.TimePeriod;
import vn.vinclub.shield.enums.TrackingDataType;

/**
 * Service for comprehensive tracking data analysis
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrackingRequestService {
    private final BloomFilterService bloomFilterService;
    private final RequestCounterService requestCounterService;

    /**
     * Perform comprehensive tracking analysis for the given context
     */
    @Profiler
    public TrackingResults analyzeTrackingRequest(TrackingContext context) {
        log.debug("[START] action={}, actionKey={}, ip={}, customers={}, devices={}",
                context.getAction(),
                context.getActionKey(),
                context.getIpAddress(),
                context.getCustomerIdentifiers(),
                context.getDeviceIdentifiers()
        );

        TrackingResults.TrackingResultsBuilder resultsBuilder = TrackingResults.builder();

        // Analyze IP address tracking
        analyzeIpTracking(context, resultsBuilder);

        // Analyze customer data tracking
        analyzeCustomerTracking(context, resultsBuilder);

        // Analyze device data tracking
        analyzeDeviceTracking(context, resultsBuilder);

        // Analyze cross-correlation patterns
        analyzeCrossCorrelationPatterns(context, resultsBuilder);

        TrackingResults results = resultsBuilder.build();
        log.debug("[COMPLETE] action={}, actionKey={}, ip={}, customerIds={}, deviceIds={} => {}",
                context.getAction(),
                context.getActionKey(),
                context.getIpAddress(),
                context.getCustomerIdentifiers(),
                context.getDeviceIdentifiers(),
                results
        );

        return results;
    }

    @Profiler
    public void putTrackingRequestData(TrackingContext context) {
        if (context.hasIpAddress()) {
            bloomFilterService.addIdentifier(context.getIpAddress(), null, TrackingDataType.IP_ADDRESS, TimePeriod.ALL_TIME);
            bloomFilterService.addIdentifier(context.getIpAddress(), null, TrackingDataType.IP_ADDRESS, TimePeriod.MONTHLY);
            bloomFilterService.addIdentifier(context.getIpAddress(), null, TrackingDataType.IP_ADDRESS, TimePeriod.DAILY);
            bloomFilterService.addIdentifier(context.getIpAddress(), context.getAction(), TrackingDataType.IP_ADDRESS, TimePeriod.DAILY);
        }

        if (context.hasCustomerData()) {
            bloomFilterService.addIdentifiers(context.getCustomerIdentifiers(), null, TrackingDataType.CUSTOMER_DATA, TimePeriod.ALL_TIME);
            bloomFilterService.addIdentifiers(context.getCustomerIdentifiers(), null, TrackingDataType.CUSTOMER_DATA, TimePeriod.MONTHLY);
            bloomFilterService.addIdentifiers(context.getCustomerIdentifiers(), null, TrackingDataType.CUSTOMER_DATA, TimePeriod.DAILY);
            bloomFilterService.addIdentifiers(context.getCustomerIdentifiers(), context.getAction(), TrackingDataType.CUSTOMER_DATA, TimePeriod.DAILY);
        }

        if (context.hasDeviceData()) {
            bloomFilterService.addIdentifiers(context.getDeviceIdentifiers(), null, TrackingDataType.DEVICE_DATA, TimePeriod.ALL_TIME);
            bloomFilterService.addIdentifiers(context.getDeviceIdentifiers(), null, TrackingDataType.DEVICE_DATA, TimePeriod.MONTHLY);
            bloomFilterService.addIdentifiers(context.getDeviceIdentifiers(), null, TrackingDataType.DEVICE_DATA, TimePeriod.DAILY);
            bloomFilterService.addIdentifiers(context.getDeviceIdentifiers(), context.getAction(), TrackingDataType.DEVICE_DATA, TimePeriod.DAILY);
        }
    }

    /**
     * Analyze IP address tracking across all time periods
     */
    private void analyzeIpTracking(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        if (!context.hasIpAddress()) {
            return;
        }

        String ipAddress = context.getIpAddress();

        // All time IP tracking
        boolean ipAddressFirstTimeSeen = bloomFilterService.notContainsIdentifier(
                ipAddress, null, TrackingDataType.IP_ADDRESS, TimePeriod.ALL_TIME);
        resultsBuilder.ipAddressFirstTimeSeen(ipAddressFirstTimeSeen);

        // Monthly IP tracking
        boolean ipAddressFirstThisMonth = bloomFilterService.notContainsIdentifier(
                ipAddress, null, TrackingDataType.IP_ADDRESS, TimePeriod.MONTHLY);
        resultsBuilder.ipAddressFirstThisMonth(ipAddressFirstThisMonth);

        // Daily IP tracking
        boolean ipAddressFirstToday = bloomFilterService.notContainsIdentifier(
                ipAddress, null, TrackingDataType.IP_ADDRESS, TimePeriod.DAILY);
        resultsBuilder.ipAddressFirstToday(ipAddressFirstToday);

        // First action by IP today
        boolean ipAddressFirstActionToday = bloomFilterService.notContainsIdentifier(
                ipAddress, context.getAction(), TrackingDataType.IP_ADDRESS, TimePeriod.DAILY);
        resultsBuilder.ipAddressFirstActionToday(ipAddressFirstActionToday);
    }


    /**
     * Analyze customer data tracking across time periods
     */
    private void analyzeCustomerTracking(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        if (!context.hasCustomerData()) {
            return;
        }

        // Customer data tracking
        boolean userIdentityFirstTimeSeen = bloomFilterService.notContainsAnyIdentifier(
                context.getCustomerIdentifiers(), null, TrackingDataType.CUSTOMER_DATA, TimePeriod.ALL_TIME);
        resultsBuilder.userIdentityFirstTimeSeen(userIdentityFirstTimeSeen);

        // Monthly customer tracking
        boolean userIdentityFirstThisMonth = bloomFilterService.notContainsAnyIdentifier(
                context.getCustomerIdentifiers(), null, TrackingDataType.CUSTOMER_DATA, TimePeriod.MONTHLY);
        resultsBuilder.userIdentityFirstThisMonth(userIdentityFirstThisMonth);

        // Daily customer tracking
        boolean userIdentityFirstToday = bloomFilterService.notContainsAnyIdentifier(
                context.getCustomerIdentifiers(), null, TrackingDataType.CUSTOMER_DATA, TimePeriod.DAILY);
        resultsBuilder.userIdentityFirstToday(userIdentityFirstToday);

        // First action by customer today
        boolean userIdentityFirstActionToday = bloomFilterService.notContainsAnyIdentifier(
                context.getCustomerIdentifiers(), context.getAction(), TrackingDataType.CUSTOMER_DATA, TimePeriod.DAILY);
        resultsBuilder.userIdentityFirstActionToday(userIdentityFirstActionToday);
    }

    /**
     * Analyze device data tracking across time periods
     */
    private void analyzeDeviceTracking(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        if (!context.hasDeviceData()) {
            return;
        }

        // Device data tracking
        boolean deviceFingerprintFirstTimeSeen = bloomFilterService.notContainsAnyIdentifier(
                context.getDeviceIdentifiers(), null, TrackingDataType.DEVICE_DATA, TimePeriod.ALL_TIME);
        resultsBuilder.deviceFingerprintFirstTimeSeen(deviceFingerprintFirstTimeSeen);

        // Monthly device tracking
        boolean deviceFingerprintFirstThisMonth = bloomFilterService.notContainsAnyIdentifier(
                context.getDeviceIdentifiers(), null, TrackingDataType.DEVICE_DATA, TimePeriod.MONTHLY);
        resultsBuilder.deviceFingerprintFirstThisMonth(deviceFingerprintFirstThisMonth);

        // Daily device tracking
        boolean deviceFingerprintFirstToday = bloomFilterService.notContainsAnyIdentifier(
                context.getDeviceIdentifiers(), null, TrackingDataType.DEVICE_DATA, TimePeriod.DAILY);
        resultsBuilder.deviceFingerprintFirstToday(deviceFingerprintFirstToday);

        // First action by device today
        boolean deviceFingerprintFirstActionToday = bloomFilterService.notContainsAnyIdentifier(
                context.getDeviceIdentifiers(), context.getAction(), TrackingDataType.DEVICE_DATA, TimePeriod.DAILY);
        resultsBuilder.deviceFingerprintFirstActionToday(deviceFingerprintFirstActionToday);
    }

    /**
     * Analyze cross-correlation patterns between different data types
     * This helps identify behavioral anomalies and suspicious patterns
     */
    private void analyzeCrossCorrelationPatterns(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        analyzeBehavioralAnomalies(context, resultsBuilder);

        analyzeVelocityPatterns(context, resultsBuilder);

        analyzeTemporalPatterns(context, resultsBuilder);
    }

    /**
     * Analyze behavioral anomalies through cross-correlation
     */
    private void analyzeBehavioralAnomalies(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        boolean newUserFromKnownNetwork = false;
        boolean knownUserFromNewNetwork = false;
        boolean newDeviceFromKnownNetwork = false;
        boolean knownDeviceFromNewNetwork = false;

        // Check if we have the necessary data for cross-correlation
        if (context.hasIpAddress()) {
            String ipAddress = context.getIpAddress();

            boolean ipKnownGlobally = bloomFilterService.containsIdentifier(
                    ipAddress, null, TrackingDataType.IP_ADDRESS, TimePeriod.ALL_TIME);

            // Analyze user-network correlation
            if (context.hasCustomerData()) {
                boolean userKnownGlobally = false;
                for (String customerId : context.getCustomerIdentifiers()) {
                    boolean userKnown = bloomFilterService.containsIdentifier(
                            customerId, null, TrackingDataType.CUSTOMER_DATA, TimePeriod.ALL_TIME);

                    if (userKnown) {
                        userKnownGlobally = true;
                        break;
                    }
                }

                // Cross-correlation analysis
                newUserFromKnownNetwork = !userKnownGlobally && ipKnownGlobally;
                knownUserFromNewNetwork = userKnownGlobally && !ipKnownGlobally;
            }

            // Analyze device-network correlation
            if (context.hasDeviceData()) {
                boolean deviceKnownGlobally = false;
                for (String deviceId : context.getDeviceIdentifiers()) {
                    boolean deviceKnown = bloomFilterService.containsIdentifier(
                            deviceId, null, TrackingDataType.DEVICE_DATA, TimePeriod.ALL_TIME);

                    if (deviceKnown) {
                        deviceKnownGlobally = true;
                        break;
                    }
                }

                // Cross-correlation analysis
                newDeviceFromKnownNetwork = !deviceKnownGlobally && ipKnownGlobally;
                knownDeviceFromNewNetwork = deviceKnownGlobally && !ipKnownGlobally;
            }
        }

        // Analyze user-device correlation
        boolean newUserFromKnownDevice = false;
        boolean knownUserFromNewDevice = false;

        if (context.hasCustomerData() && context.hasDeviceData()) {
            boolean userKnownGlobally = false;
            boolean deviceKnownGlobally = false;

            // Check if user is known
            for (String customerId : context.getCustomerIdentifiers()) {
                boolean userKnown = bloomFilterService.containsIdentifier(
                        customerId, null, TrackingDataType.CUSTOMER_DATA, TimePeriod.ALL_TIME);

                if (userKnown) {
                    userKnownGlobally = true;
                    break;
                }
            }

            // Check if device is known
            for (String deviceId : context.getDeviceIdentifiers()) {
                boolean deviceKnown = bloomFilterService.containsIdentifier(
                        deviceId, null, TrackingDataType.DEVICE_DATA, TimePeriod.ALL_TIME);
                if (deviceKnown) {
                    deviceKnownGlobally = true;
                    break;
                }
            }

            // User-device correlation analysis
            newUserFromKnownDevice = !userKnownGlobally && deviceKnownGlobally;
            knownUserFromNewDevice = userKnownGlobally && !deviceKnownGlobally;
        }

        resultsBuilder.behaviorNewUserFromKnownNetwork(newUserFromKnownNetwork);
        resultsBuilder.behaviorKnownUserFromNewNetwork(knownUserFromNewNetwork);
        resultsBuilder.behaviorNewDeviceFromKnownNetwork(newDeviceFromKnownNetwork);
        resultsBuilder.behaviorKnownDeviceFromNewNetwork(knownDeviceFromNewNetwork);
        resultsBuilder.behaviorNewUserFromKnownDevice(newUserFromKnownDevice);
        resultsBuilder.behaviorKnownUserFromNewDevice(knownUserFromNewDevice);
    }

    /**
     * Analyze velocity and frequency patterns
     */
    private void analyzeVelocityPatterns(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        boolean multipleNewIndicators = false;
        boolean highFrequencyFromNetwork = false;
        boolean unusualUserActivity = false;

        if (context.hasIpAddress()) {
            String sourceIdentifier = context.getIpAddress();

            if (context.hasCustomerData()) {
                multipleNewIndicators = requestCounterService.checkMultipleNewIndicators(
                        sourceIdentifier, context.getAction(), "customer");
            }
            if (context.hasDeviceData()) {
                multipleNewIndicators = multipleNewIndicators || requestCounterService.checkMultipleNewIndicators(
                        sourceIdentifier, context.getAction(), "device");
            }


            highFrequencyFromNetwork = requestCounterService.checkAndIncrementVelocityCounter(
                    context.getIpAddress(), context.getAction());
        }

        if (context.hasCustomerData()) {
            for (String customerId : context.getCustomerIdentifiers()) {
                if (requestCounterService.checkUnusualUserActivity(customerId, context.getAction())) {
                    unusualUserActivity = true;
                    break;
                }
            }
        }

        resultsBuilder.velocityMultipleNewIndicatorsDetected(multipleNewIndicators);
        resultsBuilder.velocityHighFrequencyFromNetwork(highFrequencyFromNetwork);
        resultsBuilder.velocityUnusualUserActivityPattern(unusualUserActivity);
    }

    /**
     * Analyze temporal patterns for suspicious timing
     */
    private void analyzeTemporalPatterns(TrackingContext context, TrackingResults.TrackingResultsBuilder resultsBuilder) {
        boolean offHoursActivity = requestCounterService.isOffHoursActivity();

        boolean automatedBehavior = false;
        boolean rapidFirePattern = false;

        if (context.hasIpAddress()) {
            automatedBehavior = requestCounterService.checkAutomatedBehaviorPattern(
                    context.getIpAddress(), context.getAction());

            rapidFirePattern = requestCounterService.checkRapidFirePattern(
                    context.getIpAddress(), context.getAction());
        }

        resultsBuilder.temporalOffHoursActivityDetected(offHoursActivity);
        resultsBuilder.temporalAutomatedBehaviorPattern(automatedBehavior);
        resultsBuilder.temporalRapidFireRequestPattern(rapidFirePattern);
    }
}
