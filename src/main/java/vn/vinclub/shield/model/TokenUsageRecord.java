package vn.vinclub.shield.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.vinclub.shield.enums.Action;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Model for storing token usage history in Redis.
 * This record tracks which tokens have been used by which key and for which actions.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TokenUsageRecord implements Serializable {

    /**
     * The token ID or hash that uniquely identifies the token.
     */
    private String tokenId;

    /**
     * The key associated with the token usage.
     */
    private String key;

    /**
     * The action for which the token was used.
     */
    private Action action;

    /**
     * The timestamp when the token was first used.
     */
    private LocalDateTime firstUsedAt;

    /**
     * The timestamp when the token was last used.
     */
    private LocalDateTime lastUsedAt;

    /**
     * The number of times the token has been used.
     */
    private int usageCount;

    /**
     * Creates a new token usage record for the first use of a token.
     *
     * @param tokenId The token ID
     * @param key The key
     * @param action The action
     * @return A new TokenUsageRecord
     */
    public static TokenUsageRecord createNew(String tokenId, String key, Action action) {
        LocalDateTime now = LocalDateTime.now();
        return TokenUsageRecord.builder()
                .tokenId(tokenId)
                .key(key)
                .action(action)
                .firstUsedAt(now)
                .lastUsedAt(now)
                .usageCount(1)
                .build();
    }

    /**
     * Updates this record for a subsequent use of the token.
     *
     * @param action The new action
     * @return This TokenUsageRecord with updated fields
     */
    public TokenUsageRecord recordUsage(Action action) {
        this.lastUsedAt = LocalDateTime.now();
        this.usageCount++;
        this.action = action;
        return this;
    }
}