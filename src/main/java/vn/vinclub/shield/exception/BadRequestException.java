package vn.vinclub.shield.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;
import vn.vinclub.shield.util.ServiceResponse;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class BadRequestException extends BusinessLogicException {
	private static final long serialVersionUID = 1L;

	public BadRequestException(ServiceResponse<?> payload) {
		super(payload);
	}

}
