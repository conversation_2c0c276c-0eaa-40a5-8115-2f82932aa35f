package vn.vinclub.shield.exception.handler;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import vn.vinclub.shield.util.ServiceResponse;

import java.io.Serial;
import java.time.Instant;
import java.util.Collections;
import java.util.List;

@SuppressWarnings({"rawtypes", "unchecked"})
@NoArgsConstructor
@AllArgsConstructor
@Getter
public class ExceptionResponse extends ServiceResponse {
    @Serial
    private static final long serialVersionUID = 1L;
    private Instant timestamp;

    public ExceptionResponse(int code, List<String> message) {
        setCode(code);
        setMessage(message);
        this.timestamp = Instant.now();
    }

    public ExceptionResponse(int code, String message) {
        setCode(code);
        setMessage(Collections.singletonList(message));
        this.timestamp = Instant.now();
    }

}
