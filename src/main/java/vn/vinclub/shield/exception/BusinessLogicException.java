package vn.vinclub.shield.exception;

import lombok.Getter;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.shield.enums.AppErrorCode;
import vn.vinclub.shield.util.ServiceResponse;

@Getter
public class BusinessLogicException extends RuntimeException {
    private static final long serialVersionUID = 1L;
    private ServiceResponse<?> payload;

    public BusinessLogicException(ServiceResponse<?> payload) {
        super(JsonUtils.toString(payload));
        this.payload = payload;

    }

    public BusinessLogicException(AppErrorCode errorCode, Object... args) {
        this(ServiceResponse.error(errorCode, args));
    }
}
