package vn.vinclub.shield.repository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Repository;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.shield.model.TokenUsageRecord;

import java.time.Duration;
import java.time.temporal.ChronoUnit;

/**
 * Repository for storing and retrieving token usage records in Redis.
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class TokenUsageRepository {

    private final RedissonClient redissonClient;
    private final BaseJsonUtils jsonUtils;

    // Key prefix for token usage records in Redis
    private static final String TOKEN_USAGE_KEY_PREFIX = "shield_svc:protect_token_usage:";

    /**
     * Saves a token usage record to Redis with a specified time-to-live.
     *
     * @param record The token usage record to save
     * @param tokenUsageTtlSeconds The time-to-live in seconds for the token usage record in Redis.
     *                            Typically calculated as token expiration time plus a buffer period (e.g., 5 minutes)
     */
    public void save(TokenUsageRecord record, long tokenUsageTtlSeconds) {
        try (var p = new Profiler(getClass(), "save")) {
            String key = getKey(record.getTokenId());
            RBucket<String> bucket = redissonClient.getBucket(key);
            bucket.set(jsonUtils.toString(record), Duration.of(tokenUsageTtlSeconds, ChronoUnit.SECONDS));
            log.debug("Saved token usage record for token ID: {}", record.getTokenId());
        }
    }

    /**
     * Retrieves a token usage record from Redis.
     *
     * @param tokenId The token ID to look up
     * @return The token usage record, or null if not found
     */
    public TokenUsageRecord findByTokenId(String tokenId) {
        try (var p = new Profiler(getClass(), "findByTokenId")) {
            String key = getKey(tokenId);
            RBucket<String> bucket = redissonClient.getBucket(key);
            String record = bucket.get();
            log.debug("Retrieved token usage record for token ID: {}, found: {}", tokenId, record != null);
            return jsonUtils.toObject(record, TokenUsageRecord.class);
        }
    }

    /**
     * Deletes a token usage record from Redis.
     *
     * @param tokenId The token ID to delete
     */
    public void delete(String tokenId) {
        try (var p = new Profiler(getClass(), "delete")) {
            String key = getKey(tokenId);
            redissonClient.getBucket(key).delete();
            log.debug("Deleted token usage record for token ID: {}", tokenId);
        }
    }

    /**
     * Generates a Redis key for a token ID.
     *
     * @param tokenId The token ID
     * @return The Redis key
     */
    private String getKey(String tokenId) {
        return TOKEN_USAGE_KEY_PREFIX + tokenId;
    }
}
