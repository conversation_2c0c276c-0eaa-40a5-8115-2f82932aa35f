apiVersion: apps/v1
kind: Deployment
metadata:
  name: vclub-shield-deployment
  namespace: vinclub-backend-stag
spec:
  replicas: 1
  progressDeadlineSeconds: 120
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1        # how many pods we can add at a time
      maxUnavailable: 0  # maxUnavailable define how many pods can be unavailable # during the rolling update
  selector:
    matchLabels:
      app: vclub-shield
  template:
    metadata:
      labels:
        app: vclub-shield
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: sv-type
                    operator: In
                    values:
                      - on-demand
                      - spot
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 1
              preference:
                matchExpressions:
                  - key: sv-type
                    operator: In
                    values:
                      - on-demand
      containers:
        - name: vclub-shield
          image: 339712890740.dkr.ecr.ap-southeast-1.amazonaws.com/vclub-shield-service:staging
          resources:
            requests:
              memory: "700Mi"
            limits:
              memory: "1000Mi"
          lifecycle:
            preStop:
              exec:
                command: [ "/bin/sh", "-ec", "sleep 45" ]
          imagePullPolicy: Always
          ports:
            - name: http-port
              containerPort: 8080
          startupProbe:
            tcpSocket:
              port: http-port
            failureThreshold: 30
            periodSeconds: 20
          envFrom:
            - configMapRef:
                name: vclub-shield
          env:
            - name: TZ
              value: "Asia/Ho_Chi_Minh"
            - name: ENV
              value: "stag"
