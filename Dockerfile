ARG AWS_ACCOUNT_ID
FROM ${AWS_ACCOUNT_ID}.dkr.ecr.ap-southeast-1.amazonaws.com/maven:3.9-eclipse-temurin-21 as builder

WORKDIR /code
COPY . /code

COPY mvnw pom.xml  ./
RUN sed -i 's/\r$//' mvnw
RUN ./mvnw dependency:go-offline -Dmaven.repo.local=.m2

COPY . .
RUN sed -i 's/\r$//' mvnw
RUN ./mvnw package -Dmaven.repo.local=.m2 -Dmaven.test.skip=true -DskipDockerMaven

FROM ${AWS_ACCOUNT_ID}.dkr.ecr.ap-southeast-1.amazonaws.com/eclipse-temurin:21-jre-alpine
ENV APP_HOME=/usr/app/
WORKDIR $APP_HOME

COPY --from=builder /code/target/*.jar ./app.jar
COPY --from=docker.elastic.co/observability/apm-agent-java:1.54.0 /usr/agent/elastic-apm-agent.jar ./elastic-apm-agent.jar

EXPOSE 8080

ENTRYPOINT java $JAVA_OPTS -jar app.jar
